import { Injectable, Logger } from '@nestjs/common'
import { Op } from '@infra-node/sequelize'
import { DatabaseService } from '../../database/database.service'
import {
  MaterialSmartDescriptionJob,
  ModelManager,
} from '../../database/models'
import { ForwardMaterialPlatformService } from '../forward/material-platform/material-platform.service'
import { LLMReadmeGeneratorAgentService } from './llm-readme-generator-agnet.service'
import { MaterialSmartDescriptionService } from '../material-smart-description/material-smart-description.service'
import { uploadToCDN } from '@/tools/cdn'
import {
  MaterialSmartDescriptionJobAttributes,
  MaterialSmartDescriptionJobStaging,
} from '@/database/models/material-smart-description-job.model'

@Injectable()
export class MaterialSmartDescriptionJobService {
  private readonly logger = new Logger(MaterialSmartDescriptionJobService.name)

  constructor(
    private readonly databaseService: DatabaseService,
    private readonly materialPlatformService: ForwardMaterialPlatformService,
    private readonly llmReadmeGeneratorService: LLMReadmeGeneratorAgentService,
    private readonly smartDescriptionService: MaterialSmartDescriptionService,
  ) {}

  async trigger(
    materialParams: Service.Forward.MaterialPlatform.MaterialIdentifierParams,
    options: {
      invokeLangBridgeBizKey: string
      invokeLangBridgeOperator: string
    },
  ): Promise<number> {
    const materialDetail
      = await this.materialPlatformService.getMaterialVersionDetail(
        materialParams,
      )
    // 将 schema 上传到 CDN
    let schemaCdnUrl = ''
    if (materialDetail.currentVersion.schema) {
      try {
        const cdnResult = await uploadToCDN({
          dir: 'material-schemas',
          filename: `material-schema-${materialDetail.id}-${materialDetail.version}.json`,
          content: JSON.stringify(
            materialDetail.currentVersion.schema,
            null,
            2,
          ),
        })
        if (!cdnResult) {
          throw new Error('返回值为空')
        }
        schemaCdnUrl = cdnResult
      }
      catch (error) {
        this.logger.warn(`上传 schema 到 CDN 失败: ${error.message}`)
      }
    }

    const jobId = await this.createEmptyJob({
      materialId: materialDetail.id,
      materialPubId: materialDetail.currentVersion.id,
      materialVersion: materialDetail.version,
      namespace: materialDetail.namespace,
      schemaUrl: schemaCdnUrl,
    })

    this.llmReadmeGeneratorService
      .analyzeMaterial(
        jobId.toString(),
        materialDetail.gitUrl,
        materialDetail.currentVersion.schema.componentName,
        {
          namespace: materialDetail.namespace,
        },
        options,
      )
      .then(async ({ rawConversation, rawResult, result }) => {
        const [conversationCdnUrl, resultCdnUrl] = await Promise.all([
          uploadToCDN({
            dir: `smart-description-job/${jobId}/conversation`,
            filename: `smart-description-job-${jobId}-conversation.md`,
            content: rawConversation,
          }),
          uploadToCDN({
            dir: `smart-description-job/${jobId}/result`,
            filename: `smart-description-job-${jobId}-result.md`,
            content: rawResult,
          }),
        ])
        const resultRecord
          = await this.smartDescriptionService.createSmartDescription({
            jobId,
            materialId: materialDetail.id,
            materialPubId: materialDetail.currentVersion.id,
            materialVersion: materialDetail.version,
            result,
            schemaUrl: schemaCdnUrl,
            namespace: materialDetail.namespace,
          })
        await this.markJobAsSucceeded(jobId, {
          rawConversation: conversationCdnUrl as string,
          rawResult: resultCdnUrl as string,
          resultId: resultRecord.id,
        })
      })
      .catch((error) => {
        this.markJobAsFailed(jobId, error.message)
        throw error
      })

    return jobId
  }

  /**
   * 批量触发智能描述生成任务
   * 并发执行，单个失败不影响整体流程
   */
  async triggerBatch(
    materials: Service.Forward.MaterialPlatform.MaterialIdentifierParams[],
    options: {
      invokeLangBridgeBizKey: string
      invokeLangBridgeOperator: string
    },
  ): Promise<Service.MaterialSmartDescription.Job.BatchTriggerResponse> {
    const total = materials.length
    let succeeded = 0
    let failed = 0
    const results: Service.MaterialSmartDescription.Job.BatchTriggerItemResult[] = []

    // 限制并发数为 5
    const concurrencyLimit = 5
    const chunks: Service.Forward.MaterialPlatform.MaterialIdentifierParams[][] = []

    // 将材料列表分块处理
    for (let i = 0; i < materials.length; i += concurrencyLimit) {
      chunks.push(materials.slice(i, i + concurrencyLimit))
    }

    // 逐块并发处理
    for (const chunk of chunks) {
      const chunkPromises = chunk.map(async (material) => {
        try {
          const jobId = await this.trigger(material, options)
          succeeded++
          return {
            material,
            success: true,
            jobId,
          }
        }
        catch (error) {
          failed++
          this.logger.error(
            `批量触发失败 - 物料: ${JSON.stringify(material)}`,
            error,
          )
          return {
            material,
            success: false,
            error: error.message || '未知错误',
          }
        }
      })

      // 等待当前块的所有任务完成
      const chunkResults = await Promise.all(chunkPromises)
      results.push(...chunkResults)
    }

    this.logger.log(
      `批量触发完成 - 总数: ${total}, 成功: ${succeeded}, 失败: ${failed}`,
    )

    return {
      total,
      succeeded,
      failed,
      results,
    }
  }

  /**
   * 创建空作业
   */
  async createEmptyJob(params: {
    materialId: number
    materialPubId: number
    materialVersion: string
    namespace: string
    schemaUrl?: string | null
  }): Promise<number> {
    const now = Date.now()
    const uuid = ModelManager.genPrimaryIndex()
    await this.databaseService.getModels().MaterialSmartDescriptionJob.create({
      id: uuid,
      materialId: params.materialId,
      materialPubId: params.materialPubId,
      materialVersion: params.materialVersion,
      staging: MaterialSmartDescriptionJobStaging.PENDING,
      createTime: now,
      state: 1,
      namespace: params.namespace,
      schemaUrl: params.schemaUrl || null,
    })
    return uuid
  }

  /**
   * 根据物料 ID 查找作业
   */
  async findJobsByMaterialId(
    materialId: string,
    limit: number = 10,
    offset: number = 0,
  ): Promise<MaterialSmartDescriptionJob[]> {
    try {
      const models = this.databaseService.getModels()
      const jobs = await models.MaterialSmartDescriptionJob.findAll({
        where: {
          materialId,
          state: 1, // 只查询正常状态的记录
        },
        limit: Number(limit),
        offset: Number(offset),
        order: [['createTime', 'DESC']],
      })
      return jobs.map(job => job.toJSON() as MaterialSmartDescriptionJob)
    }
    catch (error) {
      this.logger.error(
        `根据物料ID查找作业失败 materialId: ${materialId}`,
        error,
      )
      throw error
    }
  }

  /**
   * 根据状态查找作业
   */
  async findJobsByStatus(
    staging: MaterialSmartDescriptionJobStaging,
    limit: number = 10,
    offset: number = 0,
  ): Promise<MaterialSmartDescriptionJob[]> {
    try {
      const models = this.databaseService.getModels()
      const jobs = await models.MaterialSmartDescriptionJob.findAll({
        where: {
          staging,
          state: 1, // 只查询正常状态的记录
        },
        limit: Number(limit),
        offset: Number(offset),
        order: [['createTime', 'DESC']],
      })
      return jobs.map(job => job.toJSON() as MaterialSmartDescriptionJob)
    }
    catch (error) {
      this.logger.error(`根据状态查找作业失败 staging: ${staging}`, error)
      throw error
    }
  }

  /**
   * 获取所有作业（支持分页和过滤）
   */
  async findAllJobs(
    limit: number = 10,
    offset: number = 0,
  ): Promise<MaterialSmartDescriptionJob[]> {
    try {
      const models = this.databaseService.getModels()

      const jobs = await models.MaterialSmartDescriptionJob.findAll({
        where: {
          state: 1, // 只查询正常状态的记录
        },
        limit: Number(limit),
        offset: Number(offset),
        order: [['createTime', 'DESC']],
      })
      return jobs.map(job => job.toJSON() as MaterialSmartDescriptionJob)
    }
    catch (error) {
      this.logger.error('获取作业列表失败', error)
      throw error
    }
  }

  /**
   * 更新作业状态
   */
  async updateJobStatus(
    id: number,
    staging: MaterialSmartDescriptionJobStaging,
    failedReason?: string,
    resultId?: number,
    rawConversation?: string,
    rawResult?: string,
  ): Promise<MaterialSmartDescriptionJob | null> {
    try {
      const models = this.databaseService.getModels()
      const job = await models.MaterialSmartDescriptionJob.findByPk(id)

      if (!job) {
        return null
      }

      const updateData: Partial<
        Omit<MaterialSmartDescriptionJobAttributes, 'id'>
      > = { staging }

      if (failedReason !== undefined) updateData.failedReason = failedReason
      if (resultId !== undefined) updateData.resultId = resultId
      if (rawConversation !== undefined)
        updateData.rawConversation = rawConversation
      if (rawResult !== undefined) updateData.rawResult = rawResult

      await job.update(updateData)
      this.logger.log(`作业状态更新成功: ${job.id}, 新状态: ${staging}`)
      return job.toJSON() as MaterialSmartDescriptionJob
    }
    catch (error) {
      this.logger.error(`更新作业状态失败 ID: ${id}`, error)
      throw error
    }
  }

  /**
   * 标记作业为成功
   */
  async markJobAsSucceeded(
    id: number,
    data: {
      rawConversation: string
      rawResult: string
      resultId: number
    },
  ): Promise<MaterialSmartDescriptionJob | null> {
    return this.updateJobStatus(
      id,
      MaterialSmartDescriptionJobStaging.SUCCESS,
      undefined,
      data.resultId,
      data.rawConversation,
      data.rawResult,
    )
  }

  /**
   * 标记作业为失败
   */
  async markJobAsFailed(
    id: number,
    data: {
      failedReason: string
    },
  ): Promise<MaterialSmartDescriptionJob | null> {
    return this.updateJobStatus(
      id,
      MaterialSmartDescriptionJobStaging.FAILED,
      data.failedReason,
    )
  }

  /**
   * 统计不同状态的作业数量
   */
  async getJobStats(): Promise<{
    pending: number
    succeeded: number
    failed: number
    total: number
  }> {
    try {
      const models = this.databaseService.getModels()

      const [pending, succeeded, failed, total] = await Promise.all([
        models.MaterialSmartDescriptionJob.count({
          where: { staging: 'pending', state: 1 },
        }),
        models.MaterialSmartDescriptionJob.count({
          where: { staging: 'succeeded', state: 1 },
        }),
        models.MaterialSmartDescriptionJob.count({
          where: { staging: 'failed', state: 1 },
        }),
        models.MaterialSmartDescriptionJob.count({
          where: { state: 1 },
        }),
      ])

      return { pending, succeeded, failed, total }
    }
    catch (error) {
      this.logger.error('获取作业统计失败', error)
      throw error
    }
  }

  /**
   * 查找超时的待处理作业
   */
  async findTimeoutPendingJobs(
    timeoutMinutes: number = 30,
  ): Promise<MaterialSmartDescriptionJob[]> {
    try {
      const models = this.databaseService.getModels()
      const timeoutTimestamp = BigInt(Date.now() - timeoutMinutes * 60 * 1000)

      const jobs = await models.MaterialSmartDescriptionJob.findAll({
        where: {
          staging: 'pending',
          createTime: { $lt: timeoutTimestamp },
          state: 1, // 只查询正常状态的记录
        },
        order: [['createTime', 'ASC']],
      })

      return jobs.map(job => job.toJSON() as MaterialSmartDescriptionJob)
    }
    catch (error) {
      this.logger.error('查找超时作业失败', error)
      throw error
    }
  }

  /**
   * 查找待处理状态的作业记录
   */
  async findPendingJobs(
    limit: number = 10,
    offset: number = 0,
  ): Promise<MaterialSmartDescriptionJob[]> {
    try {
      const models = this.databaseService.getModels()
      const jobs = await models.MaterialSmartDescriptionJob.findAll({
        where: {
          state: 0, // 只查询待处理状态的记录
        },
        limit: Number(limit),
        offset: Number(offset),
        order: [['createTime', 'DESC']],
      })
      return jobs.map(job => job.toJSON() as MaterialSmartDescriptionJob)
    }
    catch (error) {
      this.logger.error('查找待处理作业失败', error)
      throw error
    }
  }

  /**
   * 根据物料 ID 查找待处理作业
   */
  async findPendingJobsByMaterialId(
    materialId: string,
    limit: number = 10,
    offset: number = 0,
  ): Promise<MaterialSmartDescriptionJob[]> {
    try {
      const models = this.databaseService.getModels()
      const jobs = await models.MaterialSmartDescriptionJob.findAll({
        where: {
          materialId,
          state: 0, // 只查询待处理状态的记录
        },
        limit: Number(limit),
        offset: Number(offset),
        order: [['createTime', 'DESC']],
      })
      return jobs.map(job => job.toJSON() as MaterialSmartDescriptionJob)
    }
    catch (error) {
      this.logger.error(
        `根据物料ID查找待处理作业失败 materialId: ${materialId}`,
        error,
      )
      throw error
    }
  }

  /**
   * 根据 materialPubId 获取历史记录
   */
  async findJobsByMaterialPubId(
    materialPubId: number,
    limit: number = 20,
    offset: number = 0,
  ): Promise<{
      jobs: MaterialSmartDescriptionJob[]
      total: number
    }> {
    try {
      const models = this.databaseService.getModels()
      const whereCondition: Record<string, unknown> = {
        materialPubId,
        state: 1, // 只查询正常状态的记录
      }

      const [jobs, total] = await Promise.all([
        models.MaterialSmartDescriptionJob.findAll({
          where: whereCondition,
          limit: Number(limit),
          offset: Number(offset),
          order: [['createTime', 'DESC']],
        }),
        models.MaterialSmartDescriptionJob.count({
          where: whereCondition,
        }),
      ])

      return {
        jobs: jobs.map(job => job.toJSON() as MaterialSmartDescriptionJob),
        total,
      }
    }
    catch (error) {
      this.logger.error(
        `根据materialPubId查找历史记录失败 materialPubId: ${materialPubId}`,
        error,
      )
      throw error
    }
  }

  /**
   * 根据 namespace 查找作业（支持模糊查询）
   */
  async findJobsByNamespace(
    namespace: string,
    limit: number = 10,
    offset: number = 0,
  ): Promise<MaterialSmartDescriptionJob[]> {
    try {
      const models = this.databaseService.getModels()
      const jobs = await models.MaterialSmartDescriptionJob.findAll({
        where: {
          namespace: {
            [Op.like]: `%${namespace}%`, // 支持模糊查询
          },
          state: 1, // 只查询正常状态的记录
        },
        limit: Number(limit),
        offset: Number(offset),
        order: [['createTime', 'DESC']],
      })
      return jobs.map(job => job.toJSON() as MaterialSmartDescriptionJob)
    }
    catch (error) {
      this.logger.error(
        `根据 namespace 模糊查找作业失败 namespace: ${namespace}`,
        error,
      )
      throw error
    }
  }

  /**
   * 根据 namespace 统计作业数量（支持模糊查询）
   */
  async countJobsByNamespace(namespace: string): Promise<number> {
    try {
      const models = this.databaseService.getModels()
      const count = await models.MaterialSmartDescriptionJob.count({
        where: {
          namespace: {
            [Op.like]: `%${namespace}%`, // 支持模糊查询
          },
          state: 1, // 只统计正常状态的记录
        },
      })
      return count
    }
    catch (error) {
      this.logger.error(
        `根据 namespace 统计作业数量失败 namespace: ${namespace}`,
        error,
      )
      throw error
    }
  }

  /**
   * 根据物料标识参数查找最新的 job 记录
   */
  async findLatestJobByMaterialIdentifier(
    materialParams: Service.Forward.MaterialPlatform.MaterialIdentifierParams,
  ): Promise<MaterialSmartDescriptionJob | null> {
    try {
      const models = this.databaseService.getModels()
      const whereCondition: Record<string, unknown> = {
        state: 1, // 只查询正常状态的记录
      }

      // 根据传入的参数构建查询条件
      if (materialParams.materialId) {
        whereCondition.materialId = materialParams.materialId
      }

      if (materialParams.materialVersionName) {
        whereCondition.materialVersion = materialParams.materialVersionName
      }

      // 如果提供了 namespace，可以直接通过 namespace 查询
      if (materialParams.namespace) {
        whereCondition.namespace = materialParams.namespace
      }

      const job = await models.MaterialSmartDescriptionJob.findOne({
        where: whereCondition,
        order: [['createTime', 'DESC']],
      })

      return job?.toJSON() as MaterialSmartDescriptionJob
    }
    catch (error) {
      this.logger.error('根据物料标识参数查找最新 job 失败', error)
      throw error
    }
  }

  /**
   * 根据物料标识参数查询最新的 job 进度
   */
  async getLatestJobStageByMaterial(
    materialParams: Service.Forward.MaterialPlatform.MaterialIdentifierParams,
  ): Promise<MaterialSmartDescriptionJobStaging> {
    try {
      // 优先通过 namespace 直接查询
      if (materialParams.namespace) {
        const latestJob = await this.findLatestJobByMaterialIdentifier(materialParams)
        if (latestJob) {
          return latestJob.staging
        }
      }

      // fallback 到原方法：通过物料平台获取 materialPubId
      const materialDetail
        = await this.materialPlatformService.getMaterialVersionDetail(
          materialParams,
        )

      const models = this.databaseService.getModels()

      // 查询该物料最新的 job 记录
      const latestJob = await models.MaterialSmartDescriptionJob.findOne({
        where: {
          materialPubId: materialDetail.currentVersion.id,
          state: 1, // 只查询正常状态的记录
        },
        order: [['createTime', 'DESC']],
      })

      // 如果没有找到 job 记录，返回 'untrigger'
      if (!latestJob) {
        return MaterialSmartDescriptionJobStaging.UNTRIGGERED
      }

      return latestJob.staging
    }
    catch (error) {
      this.logger.error(
        `查询物料最新job进度失败 materialParams: ${JSON.stringify(
          materialParams,
        )}`,
        error,
      )
      throw error
    }
  }

  /**
   * 执行原始 SQL 查询
   */
  async executeRawQuery(sql: string): Promise<unknown> {
    try {
      const result = await this.databaseService.query(sql)
      this.logger.log('执行原生 SQL 查询成功')
      return result
    }
    catch (error) {
      this.logger.error('执行原生 SQL 查询失败', error)
      throw error
    }
  }
}
